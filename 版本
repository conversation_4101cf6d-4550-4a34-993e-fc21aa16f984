mvn clean package -DskipTests
mvn spring-boot:run -DskipTests
taskkill /F /IM java.exe
2025年8月8日
重构日志模块：

对 SidebarApiController、TaskController 及各服务层代码进行日志重构，注释冗余日志输出
功能增强：
在 TaskController 中实现基于任务前缀的性能分计算逻辑
同步更新视图层展示总分统计
新增可视化组件：
在工时列表视图增加奖金分布图表
按责任人维度展示绩效分可视化数据
优化项：
改进工时列表的分页处理机制

2025年8月7日
功能：为工时记录添加责任人字段并实现数据修复功能

在 WorkHoursLog模型中新增 responsiblePerson字段，用于记录每一条工时记录的责任人。
更新 WorkHoursLogService和 WorkHoursLogServiceImpl，使其在 addWorkHours方法中支持责任人字段的处理。
在 WorkHoursLogController中新增 API 接口，用于根据关联项目或任务批量修复工时记录的责任人数据。
修改 TaskServiceImpl，使其在创建工时记录时自动关联责任人。
更新项目、任务及工时相关的 HTML 模板，以展示责任人信息。
新增 JavaScript 功能，支持从 UI 界面触发责任人数据修复流程。
编写 SQL 迁移脚本，用于在数据库中新增责任人字段并填充默认值。

2025年8月7日
[功能] 更新部署脚本以添加JAR文件备份和验证机制；更新应用版本和时间戳；优化项目表单和列表页面的布局

20250806
 feat: 更新项目视图和任务管理模板，添加总成本字段

- 在项目视图模板中，更新了视觉成本字段为单机成本1和总成本1，新增总成本2字段。
- 在任务管理模板中，修复了按钮文本以更准确地反映功能。
- 添加了数据库迁移脚本，为Projects表添加总成本1和总成本2字段，并创建索引以加速查询。
- 更新了相关文档以反映新术语和功能。

2025年8月5日
点击编辑进入任务 编辑页面后，状态框默认显示当前的状态

重构任务视图和工作流模板以提升可读性和功能性

在任务视图模板(view.html)中优化HTML结构，增强可读性和可维护性

更新工作流审批表单模板(form.html)，确保正确传递参数至工作流信息卡片片段

调整工作流信息卡片片段(workflow-info-card.html)的参数顺序并提升显示清晰度

清理工作流实例视图模板(view.html)，保持格式与结构的一致性

2025年7月30日
[功能] 更新应用版本信息，调整邓利鹏权限检查逻辑和新任务逻辑

2025年7月29日
[功能] 添加邓利鹏用户的权限检查，允许其编辑和删除项目及任务.
fix: 移除比例字段验证约束并添加总比例超限提醒

- 移除任务表单中ratio和ratedDurationDays字段的min/max验证约束
- 添加多层保护机制防止字段显示验证错误
- 增强自动计算功能确保值正确更新到表单
- 在项目详情页面添加总比例大于1时的弹窗提醒功能
- 优化表单提交验证逻辑，允许负值和大于1的比例输入"

2025年7月28日
[功能] 更新任务管理界面，添加额定工期、累计工期和剩余工期字段

2025年7月28日
在项目详情页面任务列表中添加剩余工期列显示

- 在projects/view.html中新增剩余工期表头和数据列
- 剩余工期负数时显示红色粗体样式以标识超期任务
- 更新表格列数从13列到14列
- 剩余工期数据直接从数据库remainingDurationDays字段读取

2025年7月25日
[功能] 实现任务状态选择和工作流草稿删除功能，优化界面和代码逻辑

2025年7月25日
实现任务状态选择和工作流草稿删除功能

## 新增功能

### 任务状态选择功能
- 在任务提交页面添加状态选择选项（完成/暂停）
- 实现任务暂停功能，支持保持当前进度的暂停操作
- 新增SubmitRequest.taskCompletionStatus字段支持状态传递
- 完善TaskService.pauseTask方法，包含状态验证和工期计算
- 添加暂停任务的工时记录自动创建功能

### 工作流草稿删除功能  
- 为草稿状态的工作流实例添加删除按钮
- 实现WorkflowInstanceService.deleteDraftInstance方法
- 添加严格的权限验证（仅发起人可删除自己的草稿）
- 删除草稿时自动重置关联任务的审批状态

## 界面优化
- 优化任务表单中额定工期的计算逻辑，支持双向计算
- 移除任务状态选择中的\"已暂停\"选项（通过提交页面控制）
- 更新工作流实例列表界面，添加草稿删除确认模态框
- 完善任务详情页面的状态提示和进度显示

## 代码改进
- 修复SQLiteConfig中事务管理器配置问题
- 优化SubmitApiController的状态验证逻辑
- 完善WorkflowInstanceController的参数处理
- 改进TaskServiceImpl中的审批状态处理逻辑

## 数据库相关
- 保持数据一致性，删除草稿时处理关联数据
- 优化工时记录创建，支持暂停和完成两种状态

## 文档更新
- 新增TASK_STATUS_SELECTION_IMPLEMENTATION.md详细说明
- 新增WORKFLOW_DRAFT_DELETE_IMPLEMENTATION.md功能文档
- 更新版本信息和构建时间戳"

2025年6月28日
[优化] 使用正则表达式解析流程实例备注中的扣分数量，支持正负数和小数处理

2025年6月28日
[优化] 更新审批附件处理逻辑，添加流程ID参数；
增强下载权限检查，确保只有审批记录操作人可以下载附件；
调整企业微信消息发送逻辑，动态获取Webhook URL；
修改表单默认提交说明为“请审批”

2025年6月28日[修复]
修复工作流附件下载功能，统一附件显示样式，解决附件链接路径错误问题

完善工作流审批记录的附件管理功能：
- 修复工作流附件下载路径错误，解决"NoResourceFoundException"问题
- 统一工作流审批记录与任务提交记录的附件显示样式，使用Bootstrap按钮样式
- 新增WorkflowInstanceController的附件下载端点，支持工作流附件的安全下载
- 优化附件路径处理逻辑，正确处理相对路径和绝对路径
- 增强权限控制，只允许管理员和经理下载工作流附件
- 添加用户会话临时文件跟踪器(UserSessionTempFileTracker)，用于管理文件上传会话
- 完善文件下载的活动日志记录功能
- 更新相关HTML模板，确保附件链接指向正确的下载端点

2025年6月27日[优化]
更新奖罚记录创建逻辑，添加均分积分计算及费用对冲记录功能；修改表单标题为只读并添加提示信息

2025年6月26日
功能：实现任务审批通过后自动生成工时记录

新增任务审批通过（状态为2或4）时自动创建工时记录功能
在TaskServiceImpl中集成WorkHoursLogService用于管理工时记录
实现创建前检查已有工时记录的校验逻辑
增强日志记录能力，提升工时记录生成的可追溯性
更新WorkHoursLogService接口，新增按业务类型和ID统计工时记录的方法
优化项目和任务管理服务，确保实际耗时与额定工时的准确追踪
在用户界面添加工时记录修复功能，提升数据完整性
更新HTML模板以展示新功能并改善用户体验

2025年6月26日
[修改] 更新用户活动日志记录类型，调整任务和项目状态变更的日志记录逻辑

2025年6月25日
[功能] 为项目列表添加进行中任务和任务额定工期信息

2025年6月24日
[增强] 添加提交备注验证，确保备注不能为空

2025年6月24日
[新增] 添加额定工期范围搜索条件功能

2025年6月24日
新样相机数量筛选并增强项目搜索功能

在ProjectController中实现了相机头数量范围条件的处理
更新ProjectServiceImpl，在项目筛选时处理相机数量条件
增强侧边栏导航，新增相机数量作为可搜索字段
修改项目列表和"我的项目"模板，支持相机数量输入字段
新增逻辑，在搜索条件中同时管理可视化成本、难度系数与相机数量

2025年6月22日
[增强] 更新日历事件DTO和服务实现，添加提醒信息的加载和更新逻辑

2025年6月22日
功能：新增调试API与提醒事件创建功能

实现 ​​DebugController​​，提供用于调试的人员数据与选项查询接口
新增 ​​ReminderTestController​​，支持为指定收件人创建带提醒的测试事件
优化 ​​index.html​​，向前端传递人员数据并记录调试日志
更新数据库结构，在 ​​event_reminders​​ 表中新增 ​​recipients​​ 列以支持提醒功能
提供 ​​SQL 迁移脚本​​，添加收件人列并创建索引以提升查询性能


2025年6月21日
功能更新：优化侧边栏及违规任务模板

​​权限调整​​：取消"签到管理"和"日程管理"侧边栏菜单的角色权限限制
​​报表增强​​：违规任务报表新增可点击的"任务ID"字段，支持快速跳转
​​交互优化​​：改进任务ID链接的视觉样式，强化可识别性和点击反馈
​​构建升级​​：更新构建脚本时间戳，并对备注参数实现URL编码标准化处理

2025年6月21日
[更新] 更新构建版本和时间戳，添加备注参数的URL编码功能

2025年6月19日
[更新] 修改备份脚本以获取当前日期时间，更新应用版本和时间戳，添加侧边栏API控制器和自动刷新管理器

2025年6月18日
[更新] 修改应用版本信息，添加5分钟缓存管理器以优化性能

2025年6月18日
[更新] 添加下载操作日志记录功能，修复用户活动日志表约束，优化项目和任务管理页面

2025年6月17日
[更新] 调整项目和任务管理页面的表格列宽，添加额定工期显示；更新工时日志记录逻辑；修复进度计算逻辑

2025年6月17日
功能增强：提升任务管理和项目日志功能

在 TaskController 中新增了获取项目标称持续时间的功能，用于任务创建。
实现了一个方法，用于检查标称持续时间的变化，以避免不必要的更新。
在 WorkHoursLogService 中引入了对项目标称持续时间变更的日志记录。
更新了 UserActivityLogRepository，修复了日期过滤中查询参数名称的问题。
增强了仪表盘和任务管理模板，包含原始来源追踪功能。
在项目列表页面新增了将项目数据导出为 Excel 的功能。
改进了任务表单中对标称持续时间和搜索条件的 JavaScript 处理逻辑。
清理并优化了 HTML 结构，以提高可读性和可维护性。

2025年6月16日
[更新] 修改构建版本和时间戳，新增计算任务额定工期功能

2025年6月16日
功能增强 (feat):

将文件上传大小限制提高到 50MB，以更好地处理大文件
向项目表中添加实际持续时间字段，以便更好地跟踪项目
修复 (fix):

更新工作流实例表单标签，以提高清晰度和一致性
重构 (refactor):

改进项目表单布局，并为项目类型和状态添加必填字段
增强 (enhance):

在项目视图中添加实际持续时间显示，并通过新增列改进任务列表
维护 (chore):

实现数据库备份脚本及调度，用于自动化备份
样式 (style):

更新工时模板，以显示累计工时而非库存工时

2025年6月12日
将工时管理升级为工期管理

在工时列表和查看模板中，术语从“工时”（work hours）更改为“工期”（work period）。
更新了相关标签、标题和消息以反映新术语。
为工时记录新增字段（创建人、备注、奖金），以便更好地跟踪和管理。
增强配置选项管理，新增 API 接口和服务方法。
引入新的数据库迁移，为项目和工时记录添加字段，包括单机成本1、定额工时、难度系数和摄像机数量。
将工作流审批表单中的文件上传大小限制从 10MB 提高到 50MB。
更新了工作流步骤表单中的条件表达式示例，以提高清晰度。

2025年6月11日
添加了WorkHoursLogServiceImpl，用于处理工作时间日志操作，包括保存、删除和检索日志。
创建了SQL迁移脚本，用于建立包含必要字段和索引的work_hours_log表。
开发了Thymeleaf模板，用于工作时间日志管理，包括表单、列表、视图及相关记录。
实现了前端验证和工作时间日志条目的预览功能。
为工作时间日志列表视图添加了分页和搜索能力。
通过详细视图和每条日志的操作历史，增强了用户体验。

2025年6月11日
创建了新的HTML文件temp_page.html，其中包含了完整的结构，包括头部和主体部分。

引入了Bootstrap CSS和JS，用于样式设计和功能实现。
添加了自定义样式，适用于登录容器、品牌区域和表单元素。
实现了响应式设计，通过媒体查询优化小屏幕设备上的用户体验。
集成了JavaScript进行表单验证，提升用户体验。

2025年6月9日
为日历事件实现时间格式验证和提醒功能

新增 test_event.json 用于测试时间格式的正确性。
创建 test_reminder_api.html，通过用户友好的界面测试提醒功能。
开发 test_reminder_functionality.js 以自动化测试提醒功能。
引入 test_time_format.html 进行全面的时间格式测试。
实现 time_format_test.js 以验证日历事件中时间格式的一致性。
编写 time_format_validation_report.md，详细记录验证结果与修复情况。
完成总结报告 完成总结.md，概述任务成果与系统状态。

2025年6月5日
功能更新：删除了不再使用的脚本 setup-cursor.ps1 和 setup-env.ps1。
更新 application.properties 文件，包含新构建版本和时间戳。
新增 calendartablesinitializer 用于初始化日历表和示例数据。
增强 securityconfig 以支持 API 访问，并添加 timezoneconfig 处理时区设置。
优化 webconfig 和 webmvcconfig 确保静态资源和 API 路径正确匹配。
修改 calendarcontroller 支持 “/api/calendars” 和 “/api/calendars/” 两个端点。
改进 calendardto 和日历模型的 toString 方法以便于日志记录。
统一 calendarevent 和 eventreminder 的时区处理。
在 calendareventrepository 中添加原生 SQL 查询实现时间范围搜索。
增强服务层日志与错误处理。更新 datetimeutils 以提供中国时区当前时间。
前端调整 JavaScript 包含 CSRF Token 处理，并添加 calendar-debug.js 提升调试体验。
删除已无需的 test-startup.bat 文件。

2025年6月3日
[更新] 在导出报告时添加任务ID到任务名称
2025年6月3日
[删除] 移除不再使用的SQL文件和更新应用版本信息
2025年6月3日
[优化] 更新工作流实例搜索功能，增强日志记录和参数处理

2025年5月30日
[新增] 添加日历管理页面及错误处理模板，支持事件查看、创建和编辑功能

2025年5月30日
新增日历管理页面和错误处理模板：实现了包含查看、创建和编辑事件功能的新日历管理页面；、
添加了用于事件和日历创建/编辑的模态框，配有详细表单；
新增 500 错误页面模板，用于服务器错误处理，提供用户友好的提示信息和导航选项。

2025年5月29日
[更新] 实现编译时版本信息管理，更新pom.xml和AppVersion类，添加build-info.properties文件

2025年5月28日
[新增] 更新任务审批状态方法，增加审批人姓名参数
2025年5月28日
[新增] 在活动日志中为WorkflowInstance类型添加跳转链接

2025年5月28日
[修复] 修改流程审批超时提醒通知中的发起人信息格式，并移除不必要的提示信息

2025年5月28日
[新增] 添加违规规则的扣分功能和Webhook URL字段，更新相关数据库迁移脚本
2025年5月28日
特性翻译：实现奖励和处罚记录管理

添加了RewardPenaltyRecord实体，用于表示奖励和处罚记录。
创建了RewardPenaltyRecordRepository，用于处理数据库中奖励和处罚记录的操作。
开发了RewardPenaltyRecordService，用于处理与奖励和处罚记录相关的业务逻辑。
引入了Thymeleaf方言配置，用于自定义模板处理。
更新了仪表板和违规任务模板，以包含新功能。
创建了SQL迁移脚本，用于在数据库中设置RewardPenaltyRecord表。
增强了违规任务报告的功能，并改进了UI界面。

2025年5月27日、
[新增] 添加删除流程实例和审批记录的功能，并更新相关前端模态框

2025年5月27日
[修复] 将任务状态“已取消”更改为“已暂停”，更新相关模板和逻辑

2025年5月26日
增强仪表板功能并更新依赖项：

在pom.xml中添加了jakarta.annotation-api依赖项。
改进了dashboard.html模板的布局和样式，包括警报消息和统计卡片。
重构了人员状态表，以提高可读性和功能性。
更新了人员状态表的排序功能，以正确处理各种数据类型。
在版本历史记录中添加了用于构建和运行应用程序的命令。

2025年5月26日
增强工作流实例管理和仪表板功能：

向WorkflowInstanceServiceImpl添加了用户和服务，以改进通知处理。
实现了一种通过微信和内部消息发送工作流通知的新方法。
重构了审批和提交方法，以支持动态审批人分配。
更新了仪表板HTML，包含可排序的表格，用于显示人员状态，增强了用户体验。
引入CSS样式，包括悬停效果和动画，用于表格排序。
根据工作流实例状态简化了审批表可见性逻辑。
希望此次翻译能够满足您的需求！

2025年5月26日
修复：更新按钮标签和函数注释以提高清晰度

将按钮标签从“添加时间条件”更改为“添加创建时间条件”，以明确其与创建时间相关。
更新了addtimecondition函数中的注释，以反映术语变更。

2025年5月23日
[新增] 添加工作流信息卡片处理脚本并更新字段可见性逻辑

2025年5月23日
功能特性：实现人员状态管理系统

新增 personnelstatus 实体类，用于表示人员状态记录。
创建 personnelstatusrepository 数据库交互仓库。
开发 personnelstatusservice 处理业务逻辑。
引入 personnelstatusapicontroller RESTful API 控制器。
添加 personnelstatuscontroller Web 前端控制器。
实现 personnelstatusinitializer 初始化数据。
使用 Thymeleaf 模板展示仪表盘状态信息。
自定义日期格式方言扩展。
实现日期时间工具方法。
编写 SQL 迁移脚本创建数据库表。

2025年5月23日
 [新增] 添加表单验证功能，确保必填字段填写完整
 
2025年5月23日
功能增强：新增工作流实例计数及模板获取特性

在 WorkflowInstanceRepository 中添加了按状态统计工作流实例的方法。
实现了 WorkflowInstanceService 中用于统计处理中工作流实例的服务方法。
引入了 WorkflowTemplateService 中按启用状态获取模板的方法。
更新 WorkflowInstanceServiceImpl 以包含处理实例计数逻辑。
增强 WorkflowTemplateServiceImpl 支持基于启用状态获取模板。
新增 WorkflowInterceptor 将处理实例计数添加到视图渲染的模型属性中。
改进 UI，在各类模板中显示处理中的工作流和任务数量。
添加自定义徽章样式的 CSS 类并更新 HTML 模板以反映新计数和样式。
清理部分 HTML 结构以提升可读性和可维护性。

2025年5月22日优化工作流系统功能与界面

- 修复工作流模板ID问题
- 改进工作流实例处理逻辑
- 优化用户界面显示元素
- 更新数据库结构

2025年4月21日
feat: 更新Thymeleaf模板引用，修复布局文件路径

2025年4月16日
优化日志记录，明确删除任务提交记录的描述信息

2025年4月16日
​重构任务视图模板以增强功能并提升可读性​​

更新文档类型声明以确保正确的HTML渲染
修改页面标题以动态包含任务名称
基于用户角色和任务状态增强对编辑、提交和删除按钮的权限检查
新增字段用于显示任务数量、比率和进度百分比，并采用适当格式化
使用可复用片段实现评论和提交记录的分页功能
改进删除任务、评论和提交记录的模态对话框，提供更清晰的确认信息
添加自定义JavaScript用于处理模态数据和智能返回导航

2025年4月16日
删除不再使用的日志过滤脚本和本地启动脚本，调整侧边栏和主内容区域的样式以优化布局

2025年4月15日
功能：重构项目管理和日志功能
更新LoginSuccessHandler，移除未使用的OptionsService及相关预加载逻辑，优化登录流程
引入OptionsInitializer实现应用启动时集中式选项预加载
增强AdminReportExportConfigController，添加LocalTime自定义绑定器改进导出时间配置
优化MessageController日志记录，减少冗余日志条目提升性能
简化ProjectController，移除人员列表加载的不必要日志记录
重构CacheWarmupService，保留类结构但移除实际功能（因依赖关系）
调整OptionsService取消自动预加载，将该职责委托给OptionsInitializer
改进MessageServiceImpl消息检索操作的日志冗长度
修改ProjectServiceImpl移除条件匹配过程中的不必要日志
增强ReportExportTask：新增测试任务支持手动调用，优化导出操作日志
更新application.properties：禁用Hibernate SQL日志并调整日志级别
改进logback-spring.xml配置：优化日志管理，新增文件滚动策略
增强report-export-config.html：支持按小时选择导出时间（替代固定时间输入）
新增ReportExportConfigDebugController调试控制器，支持导出配置的查询与修改

2025年4月15日
更新任务和提交记录处理，优化文件名处理逻辑，
增强活动日志页面的链接功能，支持不同实体类型的跳转。

2025年4月15日
更新项目管理系统，重构配置文件，添加通知接收人字段，
优化报表导出功能，增强错误处理和用户反馈。
更新数据库迁移脚本以支持新字段，调整前端模板以改善用户体验。

2025年4月14日
重构项目依赖和添加违规任务报表功能

- 优化pom.xml依赖管理,移除冗余依赖
- 引入Apache POI等Excel导出相关依赖
- 新增违规任务报表及导出功能
- 增加任务类型动态配置支持
- 整合Caffeine缓存提升性能

相关改进:
- 任务表单支持从配置文件动态加载任务类型
- 规范化报表导出路径配置
- 优化侧边栏菜单显示逻辑


2025年4月12日
更新项目管理系统，重构SQLite配置以支持动态数据源，优化Hibernate属性设置，
确保ORM映射资源的正确加载。删除不再使用的InitialDataLoader类和orm.xml文件，
简化项目结构。更新Project、ProjectTask、SubTask和User实体类，
添加新的字段和约束，提升数据完整性和可维护性。优化前端模板，增强用户体验，
确保数据展示的清晰和直观。同时，更新历史记录跟踪器以支持智能返回功能，
提升用户导航体验。

2025年4月12日
更新SQLite配置，调整数据源扫描包和Hibernate属性设置，
确保ORM映射资源的正确加载。同时，更新数据库文件以反映最新的变更，
提升数据管理的灵活性和准确性。

2025年4月12日
更新项目管理系统，添加用户代理工具类以获取访问设备类型，优化登录和登出日志记录，
增加设备类型信息。同时，重构多个控制器以使用基类方法获取客户端IP地址和设备类型，
提升代码复用性和可维护性。更新前端模板以显示活动日志中的终端类型，确保数据展示的清晰和直观。

2025年4月11日
更新项目管理系统，删除不再使用的搜索计划脚本，优化前端模板以支持保存和加载搜索方案的功能，
同时更新数据库文件以反映最新的变更，确保数据管理的灵活性和准确性。

2025年4月11日
更新项目管理系统，添加Spring Boot Actuator依赖，
优化提交控制器以支持提交备注自动创建评论功能，
同时更新子任务控制器以在创建子任务时自动添加评论。
删除多个不再使用的文本文件，简化项目结构。更新前端模板以提升用户体验，
确保数据管理的灵活性和准确性。

2025年4月8日
更新项目管理系统，添加单机成本1范围搜索功能，优化项目控制器以支持单机成本1的最小值和最大值条件处理。
同时，更新前端模板以允许用户输入单机成本1范围，提升用户体验。
修改数据库文件以反映新功能，确保数据管理的灵活性和准确性。

2025年4月8日
更新项目管理系统，添加保存和加载搜索方案的功能，优化前端模板以支持用户管理搜索条件。
同时，更新数据库文件以反映最新的变更，确保数据管理的灵活性和准确性。

2025年4月8日
更新项目管理系统，优化任务控制器中的分页请求逻辑，移除不必要的排序参数，
并在服务层实现自定义排序。同时，更新数据库查询以支持按任务状态和创建时间排序，
提升任务查询的灵活性和准确性。更新前端模板以反映这些变更，确保用户体验的流畅性。

2025年4月8日
删除多个不再使用的批处理脚本，包括自动构建、运行和日志过滤功能，
简化项目结构。同时，更新数据库文件以反映最新的变更。

2025年4月7日
更新项目管理系统，移除不必要的密码加密逻辑，优化用户和任务管理功能，确保用户信息的安全性和一致性。
同时，调整数据库结构以支持最后修改时间的记录，提升数据管理的灵活性和准确性。
更新前端模板以显示用户的最后修改时间，增强用户体验。

2025年4月2日
更新项目管理系统，添加视觉类型列表到项目和任务控制器的模型中，
优化前端模板以支持视觉类型的选择和展示。同时，修改数据库文件以支持新功能，
确保数据展示的清晰和直观。

2025年4月1日
更新项目管理系统，添加任务的最后评论日期功能，
优化任务控制器以支持评论天数的计算和保存。
同时，更新前端模板以显示评论天数和最后评论日期，提升用户体验。
修改数据库文件以支持新功能，确保数据展示的清晰和直观。

2025年3月31日
更新项目管理系统，添加任务提交记录功能，优化任务控制器以支持提交记录的查询和删除。
同时，更新前端模板以显示任务提交记录，提升用户体验。
修改数据库文件以支持新功能，确保数据展示的清晰和直观。

2025年3月29日
更新项目管理系统，添加评论天数条件的处理逻辑，
优化任务控制器以支持最小和最大评论天数的解析。同时，
更新前端模板以允许用户添加评论天数筛选条件，提升用户体验。
修改数据库文件以支持新功能，确保数据展示的清晰和直观。

2025年3月29日
更新项目管理系统，优化任务和子任务控制器，重构评论天数计算逻辑，
确保任务与项目之间的关联正确。同时，更新前端模板以显示项目名称和评论天数，提升用户体验。
修改数据库文件以支持新功能，确保数据展示的清晰和直观。

2025年3月28日
更新项目管理系统，优化任务和子任务控制器，添加评论天数计算功能，
确保任务与子任务之间的关联正确。同时，更新前端模板以显示评论天数，提升用户体验。
修改数据库文件以支持新功能，确保数据展示的清晰和直观。

2025年3月27日
更新项目管理系统，优化任务控制器逻辑，增强项目加载的安全性和稳定性，
确保任务与项目之间的关联正确。同时，更新前端模板以显示项目状态，
提升用户体验。修改数据库文件以支持新功能。

2025年3月27日
更新项目管理系统，添加任务评论天数计算功能，优化任务管理逻辑，增强用户体验。
同时，更新前端模板以显示评论天数，确保数据展示的清晰和直观。
修改数据库文件以支持新功能。

2025年3月27日
更新项目管理系统，修改数据库文件，优化分页模板以确保在不同页面间的跳转逻辑正确性，
增强用户体验。同时，添加备用的分页处理脚本，确保在高级搜索模式下的URL参数保持一致，
提升系统的可用性和灵活性。

2025年3月27日
更新项目管理系统，修改分页模板以使用数据属性进行页面跳转，
确保在高级搜索模式下URL路径的正确性。同时，更新任务管理和我的任务页面的跳转逻辑，
增强用户体验，确保数据展示的清晰和直观。
“我的订单任务”页面无法添加字段

2025年3月26日
更新项目管理系统，优化部署脚本，删除不再使用的文件，更新数据文件，增强用户体验，
确保数据展示的清晰和直观。同时，重构相关控制器和服务，提升系统的可用性和灵活性。

2025年3月26日
删除不再使用的批处理文件，更新数据文件，添加任务导入功能的参数选项，
优化项目和任务管理逻辑，增强用户体验，确保数据展示的清晰和直观。
同时，重构相关控制器和服务，提升系统的可用性和灵活性。

2025年3月21日
更新项目管理系统，添加任务管理页面，支持显示所有进行中任务数量，
并优化任务视图的返回逻辑。同时，更新前端模板以增强用户体验，确保数据展示的清晰和直观。

2025年3月21日
更新项目管理系统，优化项目和任务的管理逻辑，添加任务名称排序功能，增强权限检查，
确保项目和任务的创建、编辑和删除操作的安全性。同时，改进用户活动日志记录，
支持状态变更日志，提升系统的可用性和灵活性。更新前端模板以增强用户体验，
确保数据展示的清晰和直观。

2025年3月20日
更新项目管理系统，添加多个功能和优化，包括用户活动日志、项目归档、任务管理、
用户设置、报表查询等。同时，重构相关控制器和服务，改进前端模板以增强用户体验，
确保数据展示的清晰和直观，提升系统的可用性和灵活性。

2025年3月19日
更新项目管理系统，添加用户活动日志功能，包括日志记录、查询和清理操作，
优化用户体验。同时，重构相关控制器和服务，确保数据展示的清晰和直观，
增强系统的可用性和灵活性。

2025年3月18日
更新项目管理系统，添加项目归档和取消归档功能，优化项目和任务的管理逻辑，
增强用户体验。同时，新增任务类型和进度字段，改进前端模板以支持更直观的数据展示，
确保系统的灵活性和可用性。

2025年3月17日
更新项目管理系统，添加用户设置功能，包括主题样式选择和密码修改，优化用户体验。
同时，重构了相关控制器和前端模板，
确保数据展示的清晰和直观，增强了系统的可用性和灵活性。

2025年3月17日
更新项目管理系统，添加报表查询功能，包括任务统计、进度跟踪、
风险分析和工作量分析报表，优化现场任务管理，确保任务进度信息的准确性。
同时，重构了相关控制器和服务，增强了前端模板的用户体验，
确保数据展示的清晰和直观。


2025年3月17日
更新项目管理系统，添加用户管理功能，包括用户列表和用户表单页面，
支持用户的创建、编辑和删除操作。同时，优化了前端模板，增强了用户体验，
确保用户信息的有效性和安全性。

2025年3月17日
添加项目编码统一配置，包括创建.editorconfig文件、
PowerShell脚本用于检查和转换文件编码为UTF-8，
以及更新README文档以记录编码设置和维护指南。

2025年3月15日
更新项目管理系统，增强项目和任务的保存逻辑，
添加日志记录功能，优化日期处理，改进前端表单以支持隐藏字段和日期格式化，
确保状态变更时自动记录实际开始和结束时间。

2025年3月14日更新项目管理系统，升级Spring Boot版本至3.2.5，
更新SQLite JDBC和Hibernate方言依赖，优化密码修复逻辑，
添加消息通知功能，重构项目和任务的保存逻辑，
改进前端模板以支持通用侧边栏。

2025年3月14日
更新项目管理系统，升级Java版本至21，
重构安全配置，优化项目和任务的日期处理逻辑，
添加子任务管理功能，修复日期字段初始化问题，
更新前端模板以支持新字段显示。

2025年3月14日
更新项目管理系统，添加了对项目和任务的管理功能，
优化了界面和用户体验，修复了密码处理逻辑，
更新了数据库配置，增加了对多种文件类型的忽略设置。

2025年3月13日
全新创建，仅项目显示和登录正常